<?php
require_once 'functions.php';

$message = '';

if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['unsubscribe_email']) && !isset($_POST['verification_code'])) {
        $email = filter_var($_POST['unsubscribe_email'], FILTER_VALIDATE_EMAIL);
        if ($email) {
            $code = generateVerificationCode();
            if (sendUnsubscribeEmail($email, $code)) {
                $message = 'Unsubscribe verification code sent to your email!';
                // Store email in session for verification step
                if (session_status() === PHP_SESSION_NONE) {
                    session_start();
                }
                $_SESSION['pending_unsubscribe_email'] = $email;
            } else {
                $message = 'Failed to send unsubscribe verification email.';
            }
        } else {
            $message = 'Please enter a valid email address.';
        }
    } elseif (isset($_POST['verification_code'])) {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        $email = $_SESSION['pending_unsubscribe_email'] ?? '';
        $code = $_POST['verification_code'];

        if ($email && verifyUnsubscribeCode($email, $code)) {
            if (unsubscribeEmail($email)) {
                $message = 'Email successfully unsubscribed from XKCD comics!';
                unset($_SESSION['pending_unsubscribe_email']);
            } else {
                $message = 'Unsubscription failed. Please try again.';
            }
        } else {
            $message = 'Invalid verification code. Please try again.';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unsubscribe from XKCD Comics</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Plus Jakarta Sans", sans-serif;
        }

        body {
            background: radial-gradient(at 4% 39%, #bce8ff 0px, transparent 50%), radial-gradient(at 74% 6%, #6eeaff 0px, transparent 50%), radial-gradient(at 93% 52%, #ace2e1 0px, transparent 50%), radial-gradient(at 38% 38%, #f7eedd 0px, transparent 50%), #E2E2E2;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                linear-gradient(to right, rgba(236, 236, 236, 0.2) 1px, transparent 1px),
                linear-gradient(to bottom, rgba(223, 223, 223, 0.2) 1px, transparent 1px);
            background-size: 24px 24px;
            mask-image: radial-gradient(ellipse 60% 50% at 50% 0%, #000 70%, transparent 100%);
            -webkit-mask-image: radial-gradient(ellipse 60% 50% at 50% 0%, #000 70%, transparent 100%);
            pointer-events: none;
        }

        .container {
            background: rgba(255, 255, 255, 0.7);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 48px 40px;
            max-width: 500px;
            width: 100%;
            box-shadow: 0 8px 32px rgba(0, 162, 188, 0.2);
            border: 1.5px solid rgba(223, 223, 223, 0.3);
            position: relative;
            z-index: 1;
        }

        .header {
            text-align: center;
            margin-bottom: 36px;
        }

        .logo {
            width: 64px;
            height: 64px;
            background: #f8f9fa;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 28px;
            color: #495057;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }

        h1 {
            font-size: 24px;
            font-weight: 700;
            color: #212529;
            margin-bottom: 8px;
            letter-spacing: -0.5px;
        }

        .subtitle {
            color: #6c757d;
            font-size: 15px;
            font-weight: 400;
            line-height: 1.4;
        }

        .message {
            padding: 12px 16px;
            border-radius: 12px;
            margin-bottom: 24px;
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
        }

        .message i {
            margin-right: 8px;
            font-size: 14px;
        }

        .success {
            background: #d1e7dd;
            color: #0f5132;
            border: 1px solid #badbcc;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c2c7;
        }

        .form-section {
            margin-bottom: 32px;
        }

        .form-section:last-of-type {
            margin-bottom: 24px;
        }

        .step-label {
            font-size: 13px;
            font-weight: 500;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 16px;
        }

        .input-group {
            position: relative;
            margin-bottom: 16px;
        }

        .input-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #adb5bd;
            font-size: 16px;
            z-index: 2;
        }

        input[type="email"], input[type="text"] {
            width: 100%;
            padding: 16px 16px 16px 48px;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            font-size: 15px;
            font-weight: 400;
            background: #f8f9fa;
            transition: all 0.2s ease;
            outline: none;
            color: #495057;
        }

        input[type="email"]:focus, input[type="text"]:focus {
            border-color:rgb(13, 145, 253);
            background: white;
            box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.1);
        }

        input[type="email"]:focus + .input-icon, input[type="text"]:focus + .input-icon {
            color: rgb(13, 145, 253);
        }

        input::placeholder {
            color: #adb5bd;
            font-weight: 400;
        }

        .btn {
            width: 100%;
            background: #dc3545;
            color: white;
            border: none;
            padding: 16px 24px;
            border-radius: 12px;
            font-size: 15px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 16px;
        }

        .btn:hover {
            background: #c82333;
            transform: translateY(-1px);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .back-link {
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            margin-top: 24px;
            transition: color 0.2s ease;
        }

        .back-link:hover {
            color: #495057;
        }

        .back-link i {
            margin-right: 8px;
            font-size: 12px;
        }

        @media (max-width: 480px) {
            .container {
                padding: 36px 24px;
                margin: 10px;
            }
            
            h1 {
                font-size: 22px;
            }
            
            .logo {
                width: 56px;
                height: 56px;
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🚫</div>
            <h1>Unsubscribe from XKCD</h1>
            <p class="subtitle">We're sorry to see you go.<br>Confirm your unsubscription below.</p>
        </div>
        
        <?php if ($message): ?>
            <div class="message <?php echo (strpos($message, 'success') !== false || strpos($message, 'sent') !== false || strpos($message, 'unsubscribed') !== false) ? 'success' : 'error'; ?>">
                <i class="fas <?php echo (strpos($message, 'success') !== false || strpos($message, 'sent') !== false || strpos($message, 'unsubscribed') !== false) ? 'fa-check-circle' : 'fa-exclamation-triangle'; ?>"></i>
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <div class="form-section">
            <div class="step-label">Email Address</div>
            <form method="POST">
                <div class="input-group">
                    <i class="fas fa-envelope input-icon"></i>
                    <input type="email" name="unsubscribe_email" placeholder="Enter your email address" required>
                </div>
                <button type="submit" class="btn" id="submit-unsubscribe">
                    Send Verification Code
                </button>
            </form>
        </div>

        <div class="form-section">
            <div class="step-label">Verification</div>
            <form method="POST">
                <div class="input-group">
                    <i class="fas fa-shield-alt input-icon"></i>
                    <input type="text" name="verification_code" maxlength="6" placeholder="Enter 6-digit code" required>
                </div>
                <button type="submit" class="btn" id="submit-verification">
                    Complete Unsubscription
                </button>
            </form>
        </div>

        <a href="index.php" class="back-link">
            <i class="fas fa-arrow-left"></i>
            Back to subscription page
        </a>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const emailInput = document.querySelector('input[name="unsubscribe_email"]');
            const codeInput = document.querySelector('input[name="verification_code"]');
            
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    const form = this.closest('form');
                    const inputs = form.querySelectorAll('input[required]');
                    let allValid = true;
                    
                    inputs.forEach(input => {
                        if (!input.value.trim()) {
                            allValid = false;
                        }
                    });
                    
                    if (allValid) {
                        this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
                        this.disabled = true;
                    }
                });
            });
            
            // Auto-focus next input after email validation
            emailInput.addEventListener('blur', function() {
                if (this.value.includes('@') && this.value.includes('.')) {
                    setTimeout(() => {
                        codeInput.focus();
                    }, 500);
                }
            });
            
            // Format verification code input
            codeInput.addEventListener('input', function() {
                this.value = this.value.replace(/\D/g, '').substring(0, 6);
            });
        });
    </script>
</body>
</html>
