# XKCD Email Subscription System - Complete Setup Guide

This guide provides step-by-step instructions for setting up and testing the XKCD email subscription system using Mailpit for local email testing.

## Prerequisites

- PHP 8.3+ installed
- Mailpit installed on your system
- Terminal/Command line access

## Table of Contents

1. [Starting Mailpit](#1-starting-mailpit)
2. [Configuring PHP for Email Testing](#2-configuring-php-for-email-testing)
3. [Starting the XKCD Application](#3-starting-the-xkcd-application)
4. [Testing Email Verification Workflow](#4-testing-email-verification-workflow)
5. [Testing Unsubscribe Workflow](#5-testing-unsubscribe-workflow)
6. [Testing XKCD Comic Delivery](#6-testing-xkcd-comic-delivery)
7. [Setting Up the CRON Job](#7-setting-up-the-cron-job)
8. [Accessing Mailpit Web Interface](#8-accessing-mailpit-web-interface)
9. [Troubleshooting](#9-troubleshooting)

---

## 1. Starting Mailpit

### Step 1.1: Start Mailpit SMTP Server

Open a terminal and run:

```bash
mailpit
```

You should see output similar to:

```
time="2025-06-28T18:00:00Z" level=info msg="[smtp] starting on [::]:1025"
time="2025-06-28T18:00:00Z" level=info msg="[http] starting on [::]:8025"
```

**Important Notes:**

- Mailpit SMTP server runs on port **1025**
- Mailpit web interface runs on port **8025**
- Keep this terminal window open while testing

### Step 1.2: Verify Mailpit is Running

Open your browser and go to: `http://localhost:8025`

You should see the Mailpit web interface with an empty inbox.

---

## 2. Cross-Platform Email Configuration

The XKCD application automatically detects your operating system and uses the appropriate email method:

### 🪟 **Windows Systems**

- Uses PHP's built-in `mail()` function with `ini_set()` configuration
- Windows PHP respects SMTP settings and routes emails through Mailpit
- Configuration: `localhost:1025` (Mailpit's SMTP port)

### 🐧 **Linux/Unix Systems**

- Uses direct SMTP socket connection to Mailpit
- Required because Linux PHP ignores `ini_set()` SMTP settings
- Directly communicates with Mailpit's SMTP server on port 1025

### 🔧 **Technical Details**

The system automatically detects your OS using `PHP_OS` and chooses the right method:

```php
// Windows: Uses mail() function
if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
    ini_set('SMTP', 'localhost');
    ini_set('smtp_port', '1025');
    return mail($to, $subject, $body, $headers);
}
// Linux: Uses direct SMTP connection
else {
    // Direct socket connection to localhost:1025
}
```

**No manual configuration is needed** - the application automatically works on both platforms!

---

## 3. Starting the XKCD Application

### Step 3.1: Navigate to the Source Directory

```bash
cd /path/to/your/project/src
```

### Step 3.2: Start PHP Development Server

```bash
php -S localhost:8000
```

You should see:

```
PHP 8.3.x Development Server (http://localhost:8000) started
```

### Step 3.3: Access the Application

Open your browser and go to: `http://localhost:8000`

You should see the XKCD Comic Subscription page with two forms:

- Step 1: Enter Your Email
- Step 2: Enter Verification Code

---

## 4. Testing Email Verification Workflow

### Step 4.1: Submit Email Address

1. Go to `http://localhost:8000`
2. In "Step 1", enter any email address (e.g., `<EMAIL>`)
3. Click the "Submit" button

**Expected Result:** You should see "Verification code sent to your email!"

### Step 4.2: Check Email in Mailpit

1. Go to `http://localhost:8025` (Mailpit web interface)
2. You should see a new email with:
   - **From:** <EMAIL>
   - **To:** <EMAIL>
   - **Subject:** Your Verification Code
   - **Body:** "Your verification code is: **123456**" (6-digit number)

### Step 4.3: Complete Verification

1. Copy the 6-digit code from the email
2. Go back to `http://localhost:8000`
3. In "Step 2", paste the verification code
4. Click "Verify"

**Expected Result:** You should see "Email successfully registered for XKCD comics!"

### Step 4.4: Verify Registration

Check that the email was added to the registration file:

```bash
cat src/registered_emails.txt
```

You should see your test email address listed.

---

## 5. Testing Unsubscribe Workflow

### Step 5.1: Access Unsubscribe Page

Go to: `http://localhost:8000/unsubscribe.php`

### Step 5.2: Submit Email for Unsubscribe

1. Enter the same email address you registered earlier
2. Click "Unsubscribe"

**Expected Result:** "Unsubscribe verification code sent to your email!"

### Step 5.3: Check Unsubscribe Email

1. Go to `http://localhost:8025`
2. You should see a new email with:
   - **Subject:** Confirm Un-subscription
   - **Body:** "To confirm un-subscription, use this code: **654321**"

### Step 5.4: Complete Unsubscription

1. Copy the 6-digit code from the email
2. Go back to the unsubscribe page
3. Enter the verification code
4. Click "Verify"

**Expected Result:** "Email successfully unsubscribed from XKCD comics!"

---

## 6. Testing XKCD Comic Delivery

### Step 6.1: Register a Test Email

Follow steps 4.1-4.3 to register at least one email address.

### Step 6.2: Manually Trigger Comic Delivery

```bash
cd src
php cron.php
```

**Expected Output:**

```
XKCD comics sent successfully to all subscribers at 2025-06-28 18:30:00
```

### Step 6.3: Check XKCD Email

1. Go to `http://localhost:8025`
2. You should see a new email with:
   - **Subject:** Your XKCD Comic
   - **Body:** HTML content with:
     - `<h2>XKCD Comic</h2>`
     - `<img src="..." alt="XKCD Comic">`
     - `<p><a href="#" id="unsubscribe-button">Unsubscribe</a></p>`

---

## 7. Setting Up the CRON Job

### Step 7.1: Run the Setup Script

```bash
cd src
chmod +x setup_cron.sh
./setup_cron.sh
```

**Expected Output:**

```
Starting cron job setup...
Script directory: /path/to/project/src
Cron script path: /path/to/project/src/cron.php
PHP found at: /usr/bin/php
cron.php file found
Cron entry to add: 0 0 * * * /usr/bin/php /path/to/project/src/cron.php >> /tmp/xkcd_cron.log 2>&1
Cron job successfully added!
The XKCD comic will be sent to subscribers daily at midnight.
```

### Step 7.2: Verify CRON Job

```bash
crontab -l
```

You should see the XKCD cron job listed.

### Step 7.3: Check CRON Logs

```bash
tail -f /tmp/xkcd_cron.log
```

This will show the output from the daily CRON job execution.

---

## 8. Accessing Mailpit Web Interface

### Web Interface URL

`http://localhost:8025`

### Features Available:

- **Inbox:** View all received emails
- **Email Details:** Click any email to view full content
- **Search:** Search emails by sender, recipient, or subject
- **Delete:** Clear emails from the inbox
- **Raw View:** View email source code
- **API:** REST API available at `http://localhost:8025/api/v1/`

### Useful Tips:

- Emails are stored in memory only (cleared when Mailpit restarts)
- Use the search function to find specific emails quickly
- The interface auto-refreshes when new emails arrive

---

## 9. Troubleshooting

### Issue: "Failed to send verification email"

**Symptoms:** Error message appears, no email in Mailpit

**Solutions:**

1. Ensure Mailpit is running: `ps aux | grep mailpit`
2. Check Mailpit is listening on port 1025: `netstat -an | grep 1025`
3. Restart Mailpit: Kill the process and run `mailpit` again

### Issue: Mailpit web interface not accessible

**Symptoms:** Browser shows "connection refused" at `http://localhost:8025`

**Solutions:**

1. Verify Mailpit is running with web interface enabled
2. Check if port 8025 is blocked by firewall
3. Try accessing via `http://127.0.0.1:8025`

### Issue: PHP mail() function not working

**Symptoms:** No emails appear in Mailpit, PHP errors in logs

**Solutions:**

1. Check PHP error logs: `tail -f /var/log/php_errors.log`
2. Verify PHP mail configuration in `php.ini`
3. Test with a simple PHP mail script

### Issue: CRON job not executing

**Symptoms:** No emails sent at scheduled time, empty log file

**Solutions:**

1. Check cron service is running: `systemctl status cron`
2. Verify cron job syntax: `crontab -l`
3. Check cron logs: `grep CRON /var/log/syslog`
4. Ensure PHP path is correct in cron job

### Issue: Session warnings in CLI

**Symptoms:** PHP warnings about sessions when running CLI scripts

**Solutions:**
This is normal for CLI testing and doesn't affect web functionality. The warnings can be ignored for testing purposes.

### Issue: Cross-platform email problems

**Symptoms:** Emails work on one OS but not another

**Solutions:**

1. Run the connection test: `php test_mailpit_connection.php`
2. Check which email method is being used (Windows vs Linux)
3. On Windows: Ensure PHP has permission to use mail() function
4. On Linux: Ensure socket connections are allowed to localhost:1025
5. Verify Mailpit is running on both SMTP (1025) and web (8025) ports

### Issue: Windows-specific mail() problems

**Symptoms:** Windows shows "mail() function not available" or similar

**Solutions:**

1. Check if PHP mail extension is enabled: `php -m | grep mail`
2. Verify Windows firewall allows localhost connections
3. Try running PHP as administrator
4. Check Windows Event Logs for mail-related errors

### Issue: Verification codes not working

**Symptoms:** "Invalid verification code" message

**Solutions:**

1. Ensure you're using the exact code from the email
2. Check that sessions are working (cookies enabled in browser)
3. Verify the email address matches exactly

---

## Quick Test Checklist

Use this checklist to verify everything is working:

- [ ] Mailpit is running (`http://localhost:8025` accessible)
- [ ] PHP server is running (`http://localhost:8000` accessible)
- [ ] Email registration sends verification email
- [ ] Verification code works and registers email
- [ ] Unsubscribe sends verification email
- [ ] Unsubscribe verification works and removes email
- [ ] Manual XKCD delivery works (`php cron.php`)
- [ ] CRON job is installed and scheduled
- [ ] All emails appear correctly in Mailpit interface

---

## Support

If you encounter issues not covered in this guide:

1. Check the Mailpit documentation: https://mailpit.axllent.org/
2. Verify PHP mail configuration
3. Check system logs for errors
4. Ensure all required ports are available and not blocked

**Remember:** Keep Mailpit running in a separate terminal while testing the application!
